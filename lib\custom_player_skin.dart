import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:simple_pip_mode/simple_pip.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart';
import 'package:flutter_local_player/hls_utils.dart';
import 'dart:async'; // Import for StreamSubscription

class CustomPlayerSkin extends StatefulWidget {
  final VideoController controller;
  final bool hasSource;
  final bool showControls;
  final bool isFullScreenMode;
  final List<Variant> availableQualities;
  final VoidCallback? onPlayButtonPressed;

  const CustomPlayerSkin({
    super.key,
    required this.controller,
    required this.hasSource,
    required this.showControls,
    this.isFullScreenMode = false,
    this.availableQualities = const [],
    this.onPlayButtonPressed,
  });

  @override
  State<CustomPlayerSkin> createState() => _CustomPlayerSkinState();
}

class _CustomPlayerSkinState extends State<CustomPlayerSkin> {
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _currentVolume = 100.0; // Default volume
  double _previousVolume = 100.0; // To store volume before muting
  late bool _isFullScreen;
  bool _isVolumeSliderVisible = false; // New state for volume slider visibility
  bool _areControlsVisible = false; // New state for overall controls visibility
  Timer? _hideControlsTimer; // Timer for hiding controls
  late StreamSubscription<Duration> _positionSubscription;
  late StreamSubscription<Duration> _durationSubscription;
  late StreamSubscription<double> _volumeSubscription;

  // New state variables for additional features
  double _playbackSpeed = 1.0;
  List<Variant> _availableQualities = [];
  String? _currentQuality;
  List<String> _availableSubtitles = []; // Placeholder for subtitles
  String? _currentSubtitle;

  // New state variables for splash effects
  bool _showPlayPauseSplash = false;
  bool _showVolumeSplash = false;
  String _volumeSplashText = '';
  Timer? _splashTimer;

  @override
  void initState() {
    super.initState();
    _isFullScreen = widget.isFullScreenMode;
    // Only show controls if explicitly requested AND video has source
    _areControlsVisible = widget.showControls && widget.hasSource;
    _positionSubscription = widget.controller.player.stream.position.listen((
      position,
    ) {
      setState(() {
        _currentPosition = position;
      });
    });
    _durationSubscription = widget.controller.player.stream.duration.listen((
      duration,
    ) {
      setState(() {
        _totalDuration = duration;
      });
    });
    _volumeSubscription = widget.controller.player.stream.volume.listen((
      volume,
    ) {
      setState(() {
        _currentVolume = volume;
      });
    });
  }

  @override
  void dispose() {
    _positionSubscription.cancel();
    _durationSubscription.cancel();
    _volumeSubscription.cancel();
    _hideControlsTimer?.cancel(); // Cancel the timer
    _splashTimer?.cancel(); // Cancel splash timer
    super.dispose();
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _areControlsVisible = false;
      });
    });
  }

  // Picture-in-Picture functionality
  void _enterPictureInPicture() async {
    try {
      await SimplePip().enterPipMode();
    } catch (e) {
      // Handle error - PiP might not be supported
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Picture-in-Picture not supported on this device'),
          ),
        );
      }
    }
  }

  // Show subtitles selection dialog
  void _showSubtitlesDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Subtitles'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('None'),
                leading: Radio<String?>(
                  value: null,
                  groupValue: _currentSubtitle,
                  onChanged: (value) {
                    setState(() {
                      _currentSubtitle = value;
                    });
                    Navigator.of(context).pop();
                  },
                ),
              ),
              // Placeholder for actual subtitle tracks
              ListTile(
                title: const Text('English'),
                leading: Radio<String?>(
                  value: 'en',
                  groupValue: _currentSubtitle,
                  onChanged: (value) {
                    setState(() {
                      _currentSubtitle = value;
                    });
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  // Show settings dialog with playback speed and quality options
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Settings'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Playback Speed Section
                  const Text(
                    'Playback Speed',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map((speed) {
                      return ChoiceChip(
                        label: Text('${speed}x'),
                        selected: _playbackSpeed == speed,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _playbackSpeed = speed;
                            });
                            widget.controller.player.setRate(speed);
                            Navigator.of(context).pop();
                          }
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),
                  // Quality Section - now using actual HLS qualities
                  const Text(
                    'Quality',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ListTile(
                    title: const Text('Auto'),
                    leading: Radio<String?>(
                      value: null,
                      groupValue: _currentQuality,
                      onChanged: (value) {
                        setState(() {
                          _currentQuality = value;
                        });
                        Navigator.of(context).pop();
                      },
                    ),
                  ),
                  // Show available HLS qualities
                  ...widget.availableQualities.map((variant) {
                    final quality =
                        '${variant.format.width}x${variant.format.height}';
                    return ListTile(
                      title: Text(quality),
                      subtitle: Text(
                        '${(variant.format.bitrate! / 1000).round()} kbps',
                      ),
                      leading: Radio<String?>(
                        value: quality,
                        groupValue: _currentQuality,
                        onChanged: (value) {
                          setState(() {
                            _currentQuality = value;
                          });
                          // TODO: Implement quality switching
                          Navigator.of(context).pop();
                        },
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Show splash effects
  void _showPlayPauseEffect(bool isPlaying) {
    setState(() {
      _showPlayPauseSplash = true;
    });
    _splashTimer?.cancel();
    _splashTimer = Timer(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _showPlayPauseSplash = false;
        });
      }
    });
  }

  void _showVolumeEffect(double volume) {
    setState(() {
      _showVolumeSplash = true;
      _volumeSplashText = '${volume.round()}%';
    });
    _splashTimer?.cancel();
    _splashTimer = Timer(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _showVolumeSplash = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _areControlsVisible = true;
        });
        _startHideControlsTimer();
      },
      onHover: (_) {
        setState(() {
          _areControlsVisible = true;
        });
        _startHideControlsTimer();
      },
      onExit: (_) {
        _hideControlsTimer?.cancel(); // Cancel any running timer
        setState(() {
          _areControlsVisible = false; // Hide immediately on exit
        });
      },
      child: GestureDetector(
        onTap: () {
          // Handle video tap to play/pause
          if (widget.hasSource) {
            final isPlaying = widget.controller.player.state.playing;
            if (isPlaying) {
              widget.controller.player.pause();
            } else {
              widget.controller.player.play();
            }
            _showPlayPauseEffect(!isPlaying);
          }
        },
        child: Stack(
          children: [
            Video(
              controller: widget.controller,
              controls: null, // Set controls to null to remove default controls
            ),
            // Black transparent mask
            AnimatedOpacity(
              opacity: _areControlsVisible ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: IgnorePointer(
                ignoring: !_areControlsVisible,
                child: Container(color: Colors.black.withValues(alpha: 0.3)),
              ),
            ),
            // Big triangle play button - shows when video is loaded but not playing
            StreamBuilder<bool>(
              stream: widget.controller.player.stream.playing,
              builder: (context, snapshot) {
                final isPlaying = snapshot.data ?? false;
                // Show big play button when video is loaded but not playing
                if (widget.hasSource && !isPlaying) {
                  return Center(
                    child: RawMaterialButton(
                      onPressed: () {
                        widget.controller.player.play();
                        widget.onPlayButtonPressed?.call();
                        setState(() {
                          _areControlsVisible =
                              true; // Show controls after playing
                          _startHideControlsTimer(); // Start timer to hide controls
                        });
                      },
                      shape: const CircleBorder(),
                      fillColor: Colors.black.withValues(alpha: 0.3),
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: const BoxDecoration(shape: BoxShape.circle),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 48.0,
                        ),
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink(); // Hide the button when playing
              },
            ),
            AnimatedOpacity(
              opacity: _areControlsVisible ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: IgnorePointer(
                ignoring: !_areControlsVisible,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    height: 100,
                    color: Colors.transparent,
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        // Progress bar row - made more compact
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Row(
                            children: [
                              Text(
                                _currentPosition.toString().split('.').first,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(width: 8),
                              if (_totalDuration.inMilliseconds > 0)
                                Expanded(
                                  child: SliderTheme(
                                    data: SliderTheme.of(context).copyWith(
                                      trackHeight: 3.0,
                                      thumbShape: const RoundSliderThumbShape(
                                        enabledThumbRadius: 6.0,
                                      ),
                                      overlayShape:
                                          const RoundSliderOverlayShape(
                                            overlayRadius: 12.0,
                                          ),
                                    ),
                                    child: Slider(
                                      value: _currentPosition.inMilliseconds
                                          .toDouble(),
                                      min: 0.0,
                                      max: _totalDuration.inMilliseconds
                                          .toDouble(),
                                      onChanged: (value) {
                                        setState(() {
                                          _currentPosition = Duration(
                                            milliseconds: value.toInt(),
                                          );
                                        });
                                        widget.controller.player.seek(
                                          Duration(milliseconds: value.toInt()),
                                        );
                                      },
                                      activeColor: Colors.green,
                                      inactiveColor: Colors.grey,
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              Text(
                                _totalDuration.toString().split('.').first,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Volume control - improved with smaller thumb
                            MouseRegion(
                              onEnter: (_) =>
                                  setState(() => _isVolumeSliderVisible = true),
                              onExit: (_) => setState(
                                () => _isVolumeSliderVisible = false,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: Icon(
                                      _currentVolume == 0
                                          ? Icons.volume_off
                                          : Icons.volume_up,
                                      color: Colors.white,
                                      size: 20.0,
                                    ),
                                    onPressed: () {
                                      if (_currentVolume > 0) {
                                        _previousVolume = _currentVolume;
                                        widget.controller.player.setVolume(0);
                                      } else {
                                        widget.controller.player.setVolume(
                                          _previousVolume,
                                        );
                                      }
                                    },
                                  ),
                                  AnimatedOpacity(
                                    opacity: _isVolumeSliderVisible ? 1.0 : 0.0,
                                    duration: const Duration(milliseconds: 300),
                                    child: IgnorePointer(
                                      ignoring: !_isVolumeSliderVisible,
                                      child: SizedBox(
                                        width: 80, // Made more compact
                                        child: SliderTheme(
                                          data: SliderTheme.of(context).copyWith(
                                            trackHeight: 2.0,
                                            thumbShape:
                                                const RoundSliderThumbShape(
                                                  enabledThumbRadius:
                                                      4.0, // Smaller thumb
                                                ),
                                            overlayShape:
                                                const RoundSliderOverlayShape(
                                                  overlayRadius: 8.0,
                                                ),
                                          ),
                                          child: Slider(
                                            value: _currentVolume,
                                            min: 0.0,
                                            max: 100.0,
                                            onChanged: (value) {
                                              widget.controller.player
                                                  .setVolume(value);
                                              _showVolumeEffect(value);
                                            },
                                            activeColor: Colors.green,
                                            inactiveColor: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Main controls (play/pause, forward/rewind) - more compact
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  iconSize: 20.0,
                                  icon: const Icon(
                                    Icons.replay_10,
                                    color: Colors.white,
                                  ),
                                  onPressed: () {
                                    widget.controller.player.seek(
                                      _currentPosition -
                                          const Duration(seconds: 10),
                                    );
                                  },
                                ),
                                StreamBuilder<bool>(
                                  stream:
                                      widget.controller.player.stream.playing,
                                  builder: (context, snapshot) {
                                    final isPlaying = snapshot.data ?? false;
                                    return IconButton(
                                      iconSize: 32.0,
                                      icon: Icon(
                                        isPlaying
                                            ? Icons.pause
                                            : Icons.play_arrow,
                                        color: Colors.white,
                                      ),
                                      onPressed: () {
                                        if (isPlaying) {
                                          widget.controller.player.pause();
                                        } else {
                                          widget.controller.player.play();
                                        }
                                      },
                                    );
                                  },
                                ),
                                IconButton(
                                  iconSize: 20.0,
                                  icon: const Icon(
                                    Icons.forward_10,
                                    color: Colors.white,
                                  ),
                                  onPressed: () {
                                    widget.controller.player.seek(
                                      _currentPosition +
                                          const Duration(seconds: 10),
                                    );
                                  },
                                ),
                              ],
                            ),
                            // Right side controls: Subtitles > Settings > PiP > Fullscreen
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Subtitles button
                                IconButton(
                                  iconSize: 20.0,
                                  icon: const Icon(
                                    Icons.subtitles,
                                    color: Colors.white,
                                  ),
                                  onPressed: () => _showSubtitlesDialog(),
                                ),
                                // Settings button
                                IconButton(
                                  iconSize: 20.0,
                                  icon: const Icon(
                                    Icons.settings,
                                    color: Colors.white,
                                  ),
                                  onPressed: () => _showSettingsDialog(),
                                ),
                                // Picture-in-Picture button
                                IconButton(
                                  iconSize: 20.0,
                                  icon: const Icon(
                                    Icons.picture_in_picture_alt,
                                    color: Colors.white,
                                  ),
                                  onPressed: () => _enterPictureInPicture(),
                                ),
                                // Fullscreen button
                                IconButton(
                                  iconSize: 20.0,
                                  icon: Icon(
                                    _isFullScreen
                                        ? Icons.fullscreen_exit
                                        : Icons.fullscreen,
                                    color: Colors.white,
                                  ),
                                  onPressed: () {
                                    if (widget.isFullScreenMode) {
                                      // If already in fullscreen, pop the route
                                      SystemChrome.setPreferredOrientations([
                                        DeviceOrientation.portraitUp,
                                      ]);
                                      SystemChrome.setEnabledSystemUIMode(
                                        SystemUiMode.edgeToEdge,
                                      );
                                      Navigator.of(context).pop();
                                    } else {
                                      // If not in fullscreen, push the fullscreen route
                                      setState(() {
                                        _isFullScreen = !_isFullScreen;
                                      });
                                      SystemChrome.setPreferredOrientations([
                                        DeviceOrientation.landscapeLeft,
                                        DeviceOrientation.landscapeRight,
                                      ]);
                                      SystemChrome.setEnabledSystemUIMode(
                                        SystemUiMode.immersiveSticky,
                                      );
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              _VideoFullscreenPage(
                                                controller: widget.controller,
                                                hasSource: widget.hasSource,
                                                showControls:
                                                    widget.showControls,
                                                availableQualities:
                                                    widget.availableQualities,
                                                isFullScreenMode:
                                                    true, // Pass true here
                                              ),
                                        ),
                                      );
                                    }
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            // Play/Pause splash effect
            if (_showPlayPauseSplash)
              Center(
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: StreamBuilder<bool>(
                    stream: widget.controller.player.stream.playing,
                    builder: (context, snapshot) {
                      final isPlaying = snapshot.data ?? false;
                      return Icon(
                        isPlaying ? Icons.pause : Icons.play_arrow,
                        color: Colors.white,
                        size: 50,
                      );
                    },
                  ),
                ),
              ),
            // Volume splash effect
            if (_showVolumeSplash)
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _volumeSplashText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _VideoFullscreenPage extends StatelessWidget {
  final VideoController controller;
  final bool hasSource;
  final bool showControls;
  final bool isFullScreenMode;
  final List<Variant> availableQualities;

  const _VideoFullscreenPage({
    required this.controller,
    required this.hasSource,
    required this.showControls,
    this.isFullScreenMode = false,
    this.availableQualities = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: CustomPlayerSkin(
          controller: controller,
          hasSource: hasSource,
          showControls: showControls,
          isFullScreenMode: isFullScreenMode,
          availableQualities: availableQualities,
        ),
      ),
    );
  }
}
