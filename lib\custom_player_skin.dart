import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'dart:async'; // Import for StreamSubscription

class CustomPlayerSkin extends StatefulWidget {
  final VideoController controller;
  final bool hasSource;
  final bool showControls;
  final bool isFullScreenMode;
  final VoidCallback? onPlayButtonPressed;

  const CustomPlayerSkin({
    super.key,
    required this.controller,
    required this.hasSource,
    required this.showControls,
    this.isFullScreenMode = false,
    this.onPlayButtonPressed,
  });

  @override
  State<CustomPlayerSkin> createState() => _CustomPlayerSkinState();
}

class _CustomPlayerSkinState extends State<CustomPlayerSkin> {
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _currentVolume = 100.0; // Default volume
  double _previousVolume = 100.0; // To store volume before muting
  late bool _isFullScreen;
  bool _isVolumeSliderVisible = false; // New state for volume slider visibility
  bool _areControlsVisible = false; // New state for overall controls visibility
  Timer? _hideControlsTimer; // Timer for hiding controls
  late StreamSubscription<Duration> _positionSubscription;
  late StreamSubscription<Duration> _durationSubscription;
  late StreamSubscription<double> _volumeSubscription;

  @override
  void initState() {
    super.initState();
    _isFullScreen = widget.isFullScreenMode;
    _areControlsVisible =
        widget.showControls; // Initialize with external showControls
    _positionSubscription = widget.controller.player.stream.position.listen((
      position,
    ) {
      setState(() {
        _currentPosition = position;
      });
    });
    _durationSubscription = widget.controller.player.stream.duration.listen((
      duration,
    ) {
      setState(() {
        _totalDuration = duration;
      });
    });
    _volumeSubscription = widget.controller.player.stream.volume.listen((
      volume,
    ) {
      setState(() {
        _currentVolume = volume;
      });
    });
  }

  @override
  void dispose() {
    _positionSubscription.cancel();
    _durationSubscription.cancel();
    _volumeSubscription.cancel();
    _hideControlsTimer?.cancel(); // Cancel the timer
    super.dispose();
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _areControlsVisible = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        _startHideControlsTimer();
        setState(() {
          _areControlsVisible = true;
        });
      },
      onHover: (_) {
        _startHideControlsTimer();
      },
      onExit: (_) {
        _hideControlsTimer?.cancel(); // Cancel any running timer
        setState(() {
          _areControlsVisible = false; // Hide immediately on exit
        });
      },
      child: Stack(
        children: [
          Video(
            controller: widget.controller,
            controls: null, // Set controls to null to remove default controls
          ),
          // Black transparent mask
          AnimatedOpacity(
            opacity: _areControlsVisible ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: IgnorePointer(
              ignoring: !_areControlsVisible,
              child: Container(color: Colors.black.withValues(alpha: 0.3)),
            ),
          ),
          StreamBuilder<bool>(
            stream: widget.controller.player.stream.playing,
            builder: (context, snapshot) {
              final isPlaying = snapshot.data ?? false;
              if (widget.hasSource && !_areControlsVisible && !isPlaying) {
                return Center(
                  child: RawMaterialButton(
                    onPressed: () {
                      widget.controller.player.play();
                      widget.onPlayButtonPressed?.call();
                      setState(() {
                        _areControlsVisible =
                            true; // Show controls after playing
                        _startHideControlsTimer(); // Start timer to hide controls
                      });
                    },
                    shape: const CircleBorder(),
                    child: Stack(
                      children: [
                        Positioned(
                          left: 2.0,
                          top: 2.0,
                          child: Icon(
                            Icons.play_arrow,
                            color: Colors.black.withValues(alpha: 0.5),
                            size: 72.0,
                          ),
                        ),
                        const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 72.0,
                        ),
                      ],
                    ),
                  ),
                );
              }
              return const SizedBox.shrink(); // Hide the button
            },
          ),
          AnimatedOpacity(
            opacity: _areControlsVisible ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: IgnorePointer(
              ignoring: !_areControlsVisible,
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  height: 100,
                  color: Colors.transparent,
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Row(
                        children: [
                          Text(
                            _currentPosition.toString().split('.').first,
                            style: const TextStyle(color: Colors.white),
                          ),
                          if (_totalDuration.inMilliseconds > 0)
                            Expanded(
                              child: Slider(
                                value: _currentPosition.inMilliseconds
                                    .toDouble(),
                                min: 0.0,
                                max: _totalDuration.inMilliseconds.toDouble(),
                                onChanged: (value) {
                                  setState(() {
                                    _currentPosition = Duration(
                                      milliseconds: value.toInt(),
                                    );
                                  });
                                  widget.controller.player.seek(
                                    Duration(milliseconds: value.toInt()),
                                  );
                                },
                                activeColor: Colors.green,
                                inactiveColor: Colors.grey,
                              ),
                            ),
                          Text(
                            _totalDuration.toString().split('.').first,
                            style: const TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Volume control
                          MouseRegion(
                            onEnter: (_) =>
                                setState(() => _isVolumeSliderVisible = true),
                            onExit: (_) =>
                                setState(() => _isVolumeSliderVisible = false),
                            child: Row(
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _currentVolume == 0
                                        ? Icons.volume_off
                                        : Icons.volume_up,
                                    color: Colors.white,
                                    size: 24.0,
                                  ),
                                  onPressed: () {
                                    if (_currentVolume > 0) {
                                      _previousVolume = _currentVolume;
                                      widget.controller.player.setVolume(0);
                                    } else {
                                      widget.controller.player.setVolume(
                                        _previousVolume,
                                      );
                                    }
                                  },
                                ),
                                AnimatedOpacity(
                                  opacity: _isVolumeSliderVisible ? 1.0 : 0.0,
                                  duration: const Duration(milliseconds: 300),
                                  child: IgnorePointer(
                                    ignoring: !_isVolumeSliderVisible,
                                    child: SizedBox(
                                      width: 100, // Adjust width as needed
                                      child: Slider(
                                        value: _currentVolume,
                                        min: 0.0,
                                        max: 100.0,
                                        onChanged: (value) {
                                          widget.controller.player.setVolume(
                                            value,
                                          );
                                        },
                                        activeColor: Colors.green,
                                        inactiveColor: Colors.grey,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Main controls (play/pause, forward/rewind)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                icon: Stack(
                                  children: [
                                    Positioned(
                                      left: 1.0,
                                      top: 1.0,
                                      child: const Icon(
                                        Icons.replay_10,
                                        color: Colors.black,
                                        size: 24.0,
                                      ),
                                    ),
                                    const Icon(
                                      Icons.replay_10,
                                      color: Colors.white,
                                      size: 24.0,
                                    ),
                                  ],
                                ),
                                onPressed: () {
                                  widget.controller.player.seek(
                                    _currentPosition -
                                        const Duration(seconds: 10),
                                  );
                                },
                              ),
                              StreamBuilder<bool>(
                                stream: widget.controller.player.stream.playing,
                                builder: (context, snapshot) {
                                  final isPlaying = snapshot.data ?? false;
                                  return RawMaterialButton(
                                    // fillColor: Colors.white.withOpacity(0.2),
                                    shape: const CircleBorder(),
                                    onPressed: () {
                                      if (isPlaying) {
                                        widget.controller.player.pause();
                                      } else {
                                        widget.controller.player.play();
                                      }
                                    },
                                    child: Stack(
                                      children: [
                                        Positioned(
                                          left: 2.0,
                                          top: 2.0,
                                          child: Icon(
                                            isPlaying
                                                ? Icons.pause
                                                : Icons.play_arrow,
                                            color: Colors.black,
                                            size: 48.0,
                                          ),
                                        ),
                                        Icon(
                                          isPlaying
                                              ? Icons.pause
                                              : Icons.play_arrow,
                                          color: Colors.white,
                                          size: 48.0,
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                              IconButton(
                                icon: Stack(
                                  children: [
                                    Positioned(
                                      left: 1.0,
                                      top: 1.0,
                                      child: const Icon(
                                        Icons.forward_10,
                                        color: Colors.black,
                                        size: 24.0,
                                      ),
                                    ),
                                    const Icon(
                                      Icons.forward_10,
                                      color: Colors.white,
                                      size: 24.0,
                                    ),
                                  ],
                                ),
                                onPressed: () {
                                  widget.controller.player.seek(
                                    _currentPosition +
                                        const Duration(seconds: 10),
                                  );
                                },
                              ),
                            ],
                          ),
                          // Fullscreen button
                          IconButton(
                            icon: Stack(
                              children: [
                                Positioned(
                                  left: 1.0,
                                  top: 1.0,
                                  child: Icon(
                                    _isFullScreen
                                        ? Icons.zoom_out_map
                                        : Icons.zoom_in_map,
                                    color: Colors.black,
                                    size: 24.0,
                                  ),
                                ),
                                Icon(
                                  _isFullScreen
                                      ? Icons.zoom_out_map
                                      : Icons.zoom_in_map,
                                  color: Colors.white,
                                  size: 24.0,
                                ),
                              ],
                            ),
                            onPressed: () {
                              if (widget.isFullScreenMode) {
                                // If already in fullscreen, pop the route
                                SystemChrome.setPreferredOrientations([
                                  DeviceOrientation.portraitUp,
                                ]);
                                SystemChrome.setEnabledSystemUIMode(
                                  SystemUiMode.edgeToEdge,
                                );
                                Navigator.of(context).pop();
                              } else {
                                // If not in fullscreen, push the fullscreen route
                                setState(() {
                                  _isFullScreen = !_isFullScreen;
                                });
                                SystemChrome.setPreferredOrientations([
                                  DeviceOrientation.landscapeLeft,
                                  DeviceOrientation.landscapeRight,
                                ]);
                                SystemChrome.setEnabledSystemUIMode(
                                  SystemUiMode.immersiveSticky,
                                );
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => _VideoFullscreenPage(
                                      controller: widget.controller,
                                      hasSource: widget.hasSource,
                                      showControls: widget.showControls,
                                      isFullScreenMode: true, // Pass true here
                                    ),
                                  ),
                                );
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _VideoFullscreenPage extends StatelessWidget {
  final VideoController controller;
  final bool hasSource;
  final bool showControls;
  final bool isFullScreenMode;

  const _VideoFullscreenPage({
    required this.controller,
    required this.hasSource,
    required this.showControls,
    this.isFullScreenMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: CustomPlayerSkin(
          controller: controller,
          hasSource: hasSource,
          showControls: showControls,
          isFullScreenMode: isFullScreenMode,
        ),
      ),
    );
  }
}
