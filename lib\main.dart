import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:flutter_local_player/player_view.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  MediaKit.ensureInitialized();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('MediaKit Player')),
        body: const MediaKitPlayerDemo(),
      ),
    );
  }
}
