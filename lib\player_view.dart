import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:flutter_local_player/hls_utils.dart';
import 'package:flutter_local_player/custom_player_skin.dart';

class MediaKitPlayerDemo extends StatefulWidget {
  const MediaKitPlayerDemo({super.key});

  @override
  State<MediaKitPlayerDemo> createState() => _MediaKitPlayerDemoState();
}

class _MediaKitPlayerDemoState extends State<MediaKitPlayerDemo> {
  late final Player player = Player();
  late final VideoController controller = VideoController(player);
  final TextEditingController _urlController = TextEditingController();

  bool _hasSource = false;
  bool _showControls = false;

  @override
  void initState() {
    super.initState();
    _urlController.text =
        'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'; // Default URL
    player.stream.log.listen((event) {
      if (kDebugMode) {
        print('Player Log: $event');
      }
    });
    player.stream.error.listen((error) {
      if (kDebugMode) {
        print('Player Error: $error');
      }
    });
    player.stream.buffering.listen((isBuffering) {
      if (kDebugMode) {
        print('Player Buffering: $isBuffering');
      }
    });
    player.stream.playing.listen((isPlaying) {
      if (kDebugMode) {
        print('Player Playing: $isPlaying');
      }
    });
    player.stream.completed.listen((isCompleted) {
      if (kDebugMode) {
        print('Player Completed: $isCompleted');
      }
    });
  }

  @override
  void dispose() {
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: TextField(
            controller: _urlController,
            decoration: const InputDecoration(
              labelText: 'Stream URL',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            final url = _urlController.text;
            if (url.isNotEmpty) {
              if (kDebugMode) {
                print('Loading stream: $url');
              }
              if (url.endsWith('.m3u8')) {
                final variants = await getHLSVariants(url);
                if (kDebugMode) {
                  print('Available HLS Qualities:');
                  for (var i = 0; i < variants.length; i++) {
                    final variant = variants[i];
                    print(
                      '  Variant ${i + 1}: Resolution=${variant.format.width}x${variant.format.height}, Bandwidth=${variant.format.bitrate}',
                    );
                  }
                }
              }
              player.open(Media(url));
              player.play(); // Start playing immediately
              setState(() {
                _hasSource = true;
                _showControls = true; // Show controls immediately
              });
            }
          },
          child: const Text('Play Stream'),
        ),
        ElevatedButton(
          onPressed: () async {
            final url = _urlController.text;
            if (url.isNotEmpty) {
              if (kDebugMode) {
                print('Loading stream: $url');
              }
              if (url.endsWith('.m3u8')) {
                final variants = await getHLSVariants(url);
                if (kDebugMode) {
                  print('Available HLS Qualities:');
                  for (var i = 0; i < variants.length; i++) {
                    final variant = variants[i];
                    print(
                      '  Variant ${i + 1}: Resolution=${variant.format.width}x${variant.format.height}, Bandwidth=${variant.format.bitrate}',
                    );
                  }
                }
              }
              player.open(Media(url));
              setState(() {
                _hasSource = true;
                _showControls = false; // Do not show controls immediately
              });
            }
          },
          child: const Text('Load Stream'),
        ),
        Expanded(
          child: Center(
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: CustomPlayerSkin(
                controller: controller,
                hasSource: _hasSource,
                showControls: _showControls,
                onPlayButtonPressed: () {
                  setState(() {
                    _showControls = true;
                  });
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}
