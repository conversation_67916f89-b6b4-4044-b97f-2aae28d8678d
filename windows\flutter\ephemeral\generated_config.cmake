# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\dev\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Desktop\\flutter_local_player\\flutter_local_player" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\dev\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\Desktop\\flutter_local_player\\flutter_local_player"
  "FLUTTER_ROOT=C:\\dev\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\Desktop\\flutter_local_player\\flutter_local_player\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\Desktop\\flutter_local_player\\flutter_local_player"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\Desktop\\flutter_local_player\\flutter_local_player\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YmU2OThjNDhhNg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTg4MTgwMDk0OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjA="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\Desktop\\flutter_local_player\\flutter_local_player\\.dart_tool\\package_config.json"
)
